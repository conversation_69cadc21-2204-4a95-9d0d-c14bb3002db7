<html>

<head>
    <meta charset="utf-8">

    <script src="lib/bindings/utils.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css"
        integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js"
        integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>


    <center>
        <h1></h1>
    </center>

    <!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6" crossorigin="anonymous" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
        crossorigin="anonymous"></script>


    <center>
        <h1></h1>
    </center>
    <style type="text/css">
        #mynetwork {
            width: 100%;
            height: 800px;
            background-color: #ffffff;
            border: 1px solid lightgray;
            position: relative;
            float: left;
        }


        #loadingBar {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 800px;
            background-color: rgba(200, 200, 200, 0.8);
            -webkit-transition: all 0.5s ease;
            -moz-transition: all 0.5s ease;
            -ms-transition: all 0.5s ease;
            -o-transition: all 0.5s ease;
            transition: all 0.5s ease;
            opacity: 1;
        }

        #bar {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 20px;
            height: 20px;
            margin: auto auto auto auto;
            border-radius: 11px;
            border: 2px solid rgba(30, 30, 30, 0.05);
            background: rgb(0, 173, 246);
            /* Old browsers */
            box-shadow: 2px 0px 4px rgba(0, 0, 0, 0.4);
        }

        #border {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 500px;
            height: 23px;
            margin: auto auto auto auto;
            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        #text {
            position: absolute;
            top: 8px;
            left: 530px;
            width: 30px;
            height: 50px;
            margin: auto auto auto auto;
            font-size: 22px;
            color: #000000;
        }

        div.outerBorder {
            position: relative;
            top: 400px;
            width: 600px;
            height: 44px;
            margin: auto auto auto auto;
            border: 8px solid rgba(0, 0, 0, 0.1);
            background: rgb(252, 252, 252);
            /* Old browsers */
            background: -moz-linear-gradient(top, rgba(252, 252, 252, 1) 0%, rgba(237, 237, 237, 1) 100%);
            /* FF3.6+ */
            background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(252, 252, 252, 1)), color-stop(100%, rgba(237, 237, 237, 1)));
            /* Chrome,Safari4+ */
            background: -webkit-linear-gradient(top, rgba(252, 252, 252, 1) 0%, rgba(237, 237, 237, 1) 100%);
            /* Chrome10+,Safari5.1+ */
            background: -o-linear-gradient(top, rgba(252, 252, 252, 1) 0%, rgba(237, 237, 237, 1) 100%);
            /* Opera 11.10+ */
            background: -ms-linear-gradient(top, rgba(252, 252, 252, 1) 0%, rgba(237, 237, 237, 1) 100%);
            /* IE10+ */
            background: linear-gradient(to bottom, rgba(252, 252, 252, 1) 0%, rgba(237, 237, 237, 1) 100%);
            /* W3C */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fcfcfc', endColorstr='#ededed', GradientType=0);
            /* IE6-9 */
            border-radius: 72px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>


<body>
    <div class="card" style="width: 100%">


        <div id="mynetwork" class="card-body"></div>
    </div>


    <div id="loadingBar">
        <div class="outerBorder">
            <div id="text">0%</div>
            <div id="border">
                <div id="bar"></div>
            </div>
        </div>
    </div>



    <script type="text/javascript">

        // initialize global variables.
        var edges;
        var nodes;
        var allNodes;
        var allEdges;
        var nodeColors;
        var originalNodes;
        var network;
        var container;
        var options, data;
        var filter = {
            item: '',
            property: '',
            value: []
        };





        // This method is responsible for drawing the graph, returns the drawn network
        function drawGraph() {
            var container = document.getElementById('mynetwork');



            // parsing and collecting nodes and edges from the python
            nodes = new vis.DataSet([{ "color": "lightblue", "id": "http://cocon.se/actus/promo1.html", "label": "promo1.html", "shape": "dot", "size": 30, "title": "http://cocon.se/actus/promo1.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/art-cocon.html", "label": "art-cocon.html", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/art-cocon.html" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/sylvain/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/sylvain/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/christian/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/a-propos/chercheurs-lab/christian/" }, { "color": "lightblue", "id": "http://cocon.se/historique/mise-en-ligne.html", "label": "mise-en-ligne.html", "shape": "dot", "size": 10, "title": "http://cocon.se/historique/mise-en-ligne.html" }, { "color": "lightblue", "id": "http://cocon.se/historique/1s-plus-tard.html", "label": "1s-plus-tard.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/1s-plus-tard.html" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/analyse-log/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/suggestions/analyse-log/" }, { "color": "lightblue", "id": "http://cocon.se/metamots/", "label": "", "shape": "dot", "size": 70, "title": "http://cocon.se/metamots/" }, { "color": "lightblue", "id": "http://cocon.se/historique/cocon-con.html", "label": "cocon-con.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/cocon-con.html" }, { "color": "lightblue", "id": "http://cocon.se/historique/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/suggestions/" }, { "color": "lightblue", "id": "http://cocon.se/groups/crawl/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/groups/crawl/" }, { "color": "lightblue", "id": "http://cocon.se/cas/reve-cocon.html", "label": "reve-cocon.html", "shape": "dot", "size": 40, "title": "http://cocon.se/cas/reve-cocon.html" }, { "color": "lightblue", "id": "http://cocon.se/cas/le-cas-npc.html", "label": "le-cas-npc.html", "shape": "dot", "size": 40, "title": "http://cocon.se/cas/le-cas-npc.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/pas-vendredi.html", "label": "pas-vendredi.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/pas-vendredi.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/visualisation/" }, { "color": "lightblue", "id": "http://cocon.se/safari/miss-martini-rose/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/miss-martini-rose/" }, { "color": "lightblue", "id": "http://cocon.se/safari/witamine/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/safari/witamine/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/gests/thomas-leonetti/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/gests/thomas-leonetti/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/gagner-100ms/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/devs/gagner-100ms/" }, { "color": "lightblue", "id": "http://cocon.se/safari/alightweb/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/safari/alightweb/" }, { "color": "lightblue", "id": "http://cocon.se/safari/alain-kbiz/", "label": "", "shape": "dot", "size": 50, "title": "http://cocon.se/safari/alain-kbiz/" }, { "color": "lightblue", "id": "http://cocon.se/actus/promo-rentree.html", "label": "promo-rentree.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/promo-rentree.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/promo-black-friday-cocon.html", "label": "promo-black-frida...", "shape": "dot", "size": 40, "title": "http://cocon.se/actus/promo-black-friday-cocon.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/vlc-2015.html", "label": "vlc-2015.html", "shape": "dot", "size": 30, "title": "http://cocon.se/actus/vlc-2015.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/vlc-2015/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/visualisation/vlc-2015/" }, { "color": "lightblue", "id": "http://cocon.se/safari/sego_seo/", "label": "", "shape": "dot", "size": 50, "title": "http://cocon.se/safari/sego_seo/" }, { "color": "lightblue", "id": "http://cocon.se/safari/agathe/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/agathe/" }, { "color": "lightblue", "id": "http://cocon.se/safari/julie-gauthier/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/safari/julie-gauthier/" }, { "color": "lightblue", "id": "http://cocon.se/historique/ajoute-pages.html", "label": "ajoute-pages.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/ajoute-pages.html" }, { "color": "lightblue", "id": "http://cocon.se/historique/interlinking.html", "label": "interlinking.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/interlinking.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/monde-virtuel.html", "label": "monde-virtuel.html", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/monde-virtuel.html" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/a-propos/" }, { "color": "lightblue", "id": "http://cocon.se/historique/cocon-vivant.html", "label": "cocon-vivant.html", "shape": "dot", "size": 10, "title": "http://cocon.se/historique/cocon-vivant.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/tutos-cocon/crawl-site/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/visualisation/tutos-cocon/crawl-site/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/clients/ecommerce/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/clients/ecommerce/" }, { "color": "lightblue", "id": "http://cocon.se/cas/premiere-etude.html", "label": "premiere-etude.html", "shape": "dot", "size": 30, "title": "http://cocon.se/cas/premiere-etude.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/3d-ville-virtuelle/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/visualisation/3d-ville-virtuelle/" }, { "color": "lightblue", "id": "http://cocon.se/safari/christelle-milan/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/safari/christelle-milan/" }, { "color": "lightblue", "id": "http://cocon.se/cas/os-commerce-jl.html", "label": "os-commerce-jl.html", "shape": "dot", "size": 20, "title": "http://cocon.se/cas/os-commerce-jl.html" }, { "color": "lightblue", "id": "http://cocon.se/cas/ecommerce-pb.html", "label": "ecommerce-pb.html", "shape": "dot", "size": 20, "title": "http://cocon.se/cas/ecommerce-pb.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/black-friday-2019-cocon-se.html", "label": "black-friday-2019...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/black-friday-2019-cocon-se.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/black-friday-2018.html", "label": "black-friday-2018...", "shape": "dot", "size": 40, "title": "http://cocon.se/actus/black-friday-2018.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/interview-bourrelly-teknseo.html", "label": "interview-bourrel...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/interview-bourrelly-teknseo.html" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/", "label": "", "shape": "dot", "size": 40, "title": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/" }, { "color": "lightblue", "id": "http://cocon.se/cas/wordpress-avant-apres.html", "label": "wordpress-avant-a...", "shape": "dot", "size": 40, "title": "http://cocon.se/cas/wordpress-avant-apres.html" }, { "color": "lightblue", "id": "http://cocon.se/cas/petits-sites.html", "label": "petits-sites.html", "shape": "dot", "size": 10, "title": "http://cocon.se/cas/petits-sites.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/stockage-froid/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/devs/stockage-froid/" }, { "color": "lightblue", "id": "http://cocon.se/actus/gagner-tableau.html", "label": "gagner-tableau.html", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/gagner-tableau.html" }, { "color": "lightblue", "id": "http://cocon.se/sample-feature-request/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/sample-feature-request/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/scrapy/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/devs/scrapy/" }, { "color": "lightblue", "id": "http://cocon.se/concret/", "label": "", "shape": "dot", "size": 40, "title": "http://cocon.se/concret/" }, { "color": "lightblue", "id": "http://cocon.se/actus/beta-publique.html", "label": "beta-publique.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/beta-publique.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "label": "", "shape": "dot", "size": 50, "title": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/" }, { "color": "lightblue", "id": "http://cocon.se/cas/casse.html", "label": "casse.html", "shape": "dot", "size": 10, "title": "http://cocon.se/cas/casse.html" }, { "color": "lightblue", "id": "http://cocon.se/cas/astuce1.html", "label": "astuce1.html", "shape": "dot", "size": 40, "title": "http://cocon.se/cas/astuce1.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "label": "", "shape": "dot", "size": 50, "title": "http://cocon.se/cocon-semantique/images/url-dupliquees/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/canonical-cassees/", "label": "", "shape": "dot", "size": 40, "title": "http://cocon.se/cocon-semantique/images/canonical-cassees/" }, { "color": "lightblue", "id": "http://cocon.se", "label": "cocon.se", "shape": "dot", "size": 10, "title": "http://cocon.se" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/homomorphisme/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/images/homomorphisme/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/organique/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/cocon-semantique/images/organique/" }, { "color": "lightblue", "id": "http://cocon.se/cas/le-cas-lks.html", "label": "le-cas-lks.html", "shape": "dot", "size": 20, "title": "http://cocon.se/cas/le-cas-lks.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/images/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/gests/doeurf/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/gests/doeurf/" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/near-duplicate/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/suggestions/near-duplicate/" }, { "color": "lightblue", "id": "http://cocon.se/actus/cocon-et-covid19.html", "label": "cocon-et-covid19....", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/cocon-et-covid19.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/sophie-la-g-raph/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/sophie-la-g-raph/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/graphe-firebase/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/devs/graphe-firebase/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/devs/" }, { "color": "lightblue", "id": "http://cocon.se/actus/quelques-news.html", "label": "quelques-news.html", "shape": "dot", "size": 30, "title": "http://cocon.se/actus/quelques-news.html" }, { "color": "lightblue", "id": "http://cocon.se/historique/point-dimanche.html", "label": "point-dimanche.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/point-dimanche.html" }, { "color": "lightblue", "id": "http://cocon.se/safari/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/" }, { "color": "lightblue", "id": "http://cocon.se/safari/annick-marie/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/safari/annick-marie/" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/export-freemind/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/suggestions/export-freemind/" }, { "color": "lightblue", "id": "http://cocon.se/actus/cyber-monday.html", "label": "cyber-monday.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/cyber-monday.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/cache-cocon.html", "label": "cache-cocon.html", "shape": "dot", "size": 40, "title": "http://cocon.se/actus/cache-cocon.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/promo-rebelote.html", "label": "promo-rebelote.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/promo-rebelote.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/bientot.html", "label": "bientot.html", "shape": "dot", "size": 40, "title": "http://cocon.se/actus/bientot.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/mesurer-ttfb/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/devs/mesurer-ttfb/" }, { "color": "lightblue", "id": "http://cocon.se/cas/jouer.html", "label": "jouer.html", "shape": "dot", "size": 20, "title": "http://cocon.se/cas/jouer.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/seos-heros-data.html", "label": "seos-heros-data.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/seos-heros-data.html" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/clients/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/clients/" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/tutos-cocon/trouver-url/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/visualisation/tutos-cocon/trouver-url/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/cocon-et-cluster/", "label": "", "shape": "dot", "size": 30, "title": "http://cocon.se/cocon-semantique/images/cocon-et-cluster/" }, { "color": "lightblue", "id": "http://cocon.se/actus/aujourdhui-demain.html", "label": "aujourdhui-demain...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/aujourdhui-demain.html" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/tutos-cocon/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/visualisation/tutos-cocon/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/inutiles/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/images/inutiles/" }, { "color": "lightblue", "id": "http://cocon.se/cas/mot-cle-concurrentiel.html", "label": "mot-cle-concurren...", "shape": "dot", "size": 10, "title": "http://cocon.se/cas/mot-cle-concurrentiel.html" }, { "color": "lightblue", "id": "http://cocon.se/historique/cocon-pousse.html", "label": "cocon-pousse.html", "shape": "dot", "size": 20, "title": "http://cocon.se/historique/cocon-pousse.html" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/microdatas/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/suggestions/microdatas/" }, { "color": "lightblue", "id": "http://cocon.se/actus/etudes-vlc.html", "label": "etudes-vlc.html", "shape": "dot", "size": 30, "title": "http://cocon.se/actus/etudes-vlc.html" }, { "color": "lightblue", "id": "http://cocon.se/safari/baara-digital/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/baara-digital/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/cocon-seo/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/cocon-seo/" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cocon-semantique/" }, { "color": "lightblue", "id": "http://cocon.se/actus/dimension-fractale.html", "label": "dimension-fractal...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/dimension-fractale.html" }, { "color": "lightblue", "id": "http://cocon.se/actus/vitesse-superieure.html", "label": "vitesse-superieur...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/vitesse-superieure.html" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/faire-un-don/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/faire-un-don/" }, { "color": "lightblue", "id": "http://cocon.se/visualisation/visu-cmap/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/visualisation/visu-cmap/" }, { "color": "lightblue", "id": "http://cocon.se/actus/meta-promo-48h.html", "label": "meta-promo-48h.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/meta-promo-48h.html" }, { "color": "lightblue", "id": "http://cocon.se/concret/pre-prod/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/concret/pre-prod/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/clients/philippe-donnart/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/clients/philippe-donnart/" }, { "color": "lightblue", "id": "http://cocon.se/actus/analyse-logs-bots.html", "label": "analyse-logs-bots...", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/analyse-logs-bots.html" }, { "color": "lightblue", "id": "http://cocon.se/concret/logs-bea/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/concret/logs-bea/" }, { "color": "lightblue", "id": "http://cocon.se/actus/safari-deauville.html", "label": "safari-deauville....", "shape": "dot", "size": 30, "title": "http://cocon.se/actus/safari-deauville.html" }, { "color": "lightblue", "id": "http://cocon.se/safari/autres/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/autres/" }, { "color": "lightblue", "id": "http://cocon.se/actus/cocon-profitez.html", "label": "cocon-profitez.html", "shape": "dot", "size": 20, "title": "http://cocon.se/actus/cocon-profitez.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/images/cocon-feu/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/images/cocon-feu/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/gests/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/gests/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/gests/jerome-rossignol/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/a-propos/chercheurs-lab/gests/jerome-rossignol/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/clients/viaprestige/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/clients/viaprestige/" }, { "color": "lightblue", "id": "http://cocon.se/cas/", "label": "", "shape": "dot", "size": 20, "title": "http://cocon.se/cas/" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/crawl-plusieurs-hosts/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/suggestions/crawl-plusieurs-hosts/" }, { "color": "lightblue", "id": "http://cocon.se/actus/optimiser-recherche.html", "label": "optimiser-recherc...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/optimiser-recherche.html" }, { "color": "lightblue", "id": "http://cocon.se/groups/metamots/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/groups/metamots/" }, { "color": "lightblue", "id": "http://cocon.se/actus/seo-camp-metamots.html", "label": "seo-camp-metamots...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/seo-camp-metamots.html" }, { "color": "lightblue", "id": "http://cocon.se/cocon-semantique/devs/optimisation-php/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/cocon-semantique/devs/optimisation-php/" }, { "color": "lightblue", "id": "http://cocon.se/actus/mai-fais-te-plait.html", "label": "mai-fais-te-plait...", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/mai-fais-te-plait.html" }, { "color": "lightblue", "id": "http://cocon.se/suggestions/coeurs-cocon/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/suggestions/coeurs-cocon/" }, { "color": "lightblue", "id": "http://cocon.se/safari/nico-earth/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/safari/nico-earth/" }, { "color": "lightblue", "id": "http://cocon.se/a-propos/chercheurs-lab/gests/christian-meline/", "label": "", "shape": "dot", "size": 10, "title": "http://cocon.se/a-propos/chercheurs-lab/gests/christian-meline/" }, { "color": "lightblue", "id": "http://cocon.se/actus/partager-crawls.html", "label": "partager-crawls.html", "shape": "dot", "size": 10, "title": "http://cocon.se/actus/partager-crawls.html" }]);
            edges = new vis.DataSet([{ "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/promo1.html", "id": "0", "title": "Similarit\u00e9 : 0.605", "to": "http://cocon.se/actus/art-cocon.html", "width": 3.0912818917618674 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/sylvain/", "id": "1", "title": "Similarit\u00e9 : 0.613", "to": "http://cocon.se/a-propos/chercheurs-lab/christian/", "width": 3.2141155208293264 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/mise-en-ligne.html", "id": "2", "title": "Similarit\u00e9 : 0.642", "to": "http://cocon.se/historique/1s-plus-tard.html", "width": 3.6416138331100694 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/analyse-log/", "id": "3", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/cocon-con.html", "id": "4", "title": "Similarit\u00e9 : 0.895", "to": "http://cocon.se/historique/", "width": 7.430057513458551 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/", "id": "5", "title": "Similarit\u00e9 : 0.918", "to": "http://cocon.se/groups/crawl/", "width": 7.767745413333735 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/reve-cocon.html", "id": "6", "title": "Similarit\u00e9 : 0.735", "to": "http://cocon.se/cas/le-cas-npc.html", "width": 5.031240672947045 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/pas-vendredi.html", "id": "7", "title": "Similarit\u00e9 : 0.535", "to": "http://cocon.se/actus/promo1.html", "width": 2.0441139432090134 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/", "id": "8", "title": "Similarit\u00e9 : 0.683", "to": "http://cocon.se/cas/reve-cocon.html", "width": 4.259490668347976 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/", "id": "9", "title": "Similarit\u00e9 : 0.895", "to": "http://cocon.se/historique/cocon-con.html", "width": 7.430057513458551 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/miss-martini-rose/", "id": "10", "title": "Similarit\u00e9 : 0.625", "to": "http://cocon.se/safari/witamine/", "width": 3.380957509612056 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/gests/thomas-leonetti/", "id": "11", "title": "Similarit\u00e9 : 0.594", "to": "http://cocon.se/cocon-semantique/devs/gagner-100ms/", "width": 2.9195699061399534 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/alightweb/", "id": "12", "title": "Similarit\u00e9 : 0.765", "to": "http://cocon.se/safari/alain-kbiz/", "width": 5.476368195397474 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/promo-rentree.html", "id": "13", "title": "Similarit\u00e9 : 0.601", "to": "http://cocon.se/actus/promo-black-friday-cocon.html", "width": 3.0279371471607575 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/vlc-2015.html", "id": "14", "title": "Similarit\u00e9 : 0.699", "to": "http://cocon.se/visualisation/vlc-2015/", "width": 4.501095136079508 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/alain-kbiz/", "id": "15", "title": "Similarit\u00e9 : 0.824", "to": "http://cocon.se/safari/sego_seo/", "width": 6.370892069029529 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/agathe/", "id": "16", "title": "Similarit\u00e9 : 0.662", "to": "http://cocon.se/safari/julie-gauthier/", "width": 3.947112480983543 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/ajoute-pages.html", "id": "17", "title": "Similarit\u00e9 : 0.752", "to": "http://cocon.se/historique/interlinking.html", "width": 5.290543030290867 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/monde-virtuel.html", "id": "18", "title": "Similarit\u00e9 : 0.609", "to": "http://cocon.se/a-propos/", "width": 3.142484646075098 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/cocon-vivant.html", "id": "19", "title": "Similarit\u00e9 : 0.565", "to": "http://cocon.se/visualisation/tutos-cocon/crawl-site/", "width": 2.4916488139138306 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/clients/ecommerce/", "id": "20", "title": "Similarit\u00e9 : 0.688", "to": "http://cocon.se/cas/premiere-etude.html", "width": 4.332567379160137 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/3d-ville-virtuelle/", "id": "21", "title": "Similarit\u00e9 : 0.559", "to": "http://cocon.se/actus/monde-virtuel.html", "width": 2.4005870085475802 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/christelle-milan/", "id": "22", "title": "Similarit\u00e9 : 0.758", "to": "http://cocon.se/safari/sego_seo/", "width": 5.379897660940964 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/os-commerce-jl.html", "id": "23", "title": "Similarit\u00e9 : 0.739", "to": "http://cocon.se/cas/ecommerce-pb.html", "width": 5.101258026770489 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/black-friday-2019-cocon-se.html", "id": "24", "title": "Similarit\u00e9 : 0.785", "to": "http://cocon.se/actus/black-friday-2018.html", "width": 5.776480412701014 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/interview-bourrelly-teknseo.html", "id": "25", "title": "Similarit\u00e9 : 0.634", "to": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/", "width": 3.5197791777532124 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/gagner-100ms/", "id": "26", "title": "Similarit\u00e9 : 0.639", "to": "http://cocon.se/cas/wordpress-avant-apres.html", "width": 3.5926364711253673 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/petits-sites.html", "id": "27", "title": "Similarit\u00e9 : 0.678", "to": "http://cocon.se/cas/le-cas-npc.html", "width": 4.18135665312589 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/stockage-froid/", "id": "28", "title": "Similarit\u00e9 : 0.496", "to": "http://cocon.se/actus/gagner-tableau.html", "width": 1.4652559507782463 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/sample-feature-request/", "id": "29", "title": "Similarit\u00e9 : 0.898", "to": "http://cocon.se/groups/crawl/", "width": 7.470841507134691 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/scrapy/", "id": "30", "title": "Similarit\u00e9 : 0.683", "to": "http://cocon.se/concret/", "width": 4.255899714634686 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/beta-publique.html", "id": "31", "title": "Similarit\u00e9 : 0.695", "to": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "width": 4.435740848826624 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/casse.html", "id": "32", "title": "Similarit\u00e9 : 0.663", "to": "http://cocon.se/cas/astuce1.html", "width": 3.9503805521225126 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "id": "33", "title": "Similarit\u00e9 : 0.842", "to": "http://cocon.se/cocon-semantique/images/canonical-cassees/", "width": 6.633317111159796 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se", "id": "34", "title": "Similarit\u00e9 : 0.646", "to": "http://cocon.se/cocon-semantique/devs/scrapy/", "width": 3.7066042085112336 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/homomorphisme/", "id": "35", "title": "Similarit\u00e9 : 0.767", "to": "http://cocon.se/cocon-semantique/images/organique/", "width": 5.518351849467149 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/wordpress-avant-apres.html", "id": "36", "title": "Similarit\u00e9 : 0.747", "to": "http://cocon.se/cas/le-cas-lks.html", "width": 5.217759768006195 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/", "id": "37", "title": "Similarit\u00e9 : 0.766", "to": "http://cocon.se/cocon-semantique/images/canonical-cassees/", "width": 5.5001393100057365 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/premiere-etude.html", "id": "38", "title": "Similarit\u00e9 : 0.725", "to": "http://cocon.se/cas/le-cas-npc.html", "width": 4.889267777913427 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/gests/doeurf/", "id": "39", "title": "Similarit\u00e9 : 0.565", "to": "http://cocon.se/safari/sego_seo/", "width": 2.4922169802117358 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/julie-gauthier/", "id": "40", "title": "Similarit\u00e9 : 0.667", "to": "http://cocon.se/safari/christelle-milan/", "width": 4.0119039535834 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/promo-black-friday-cocon.html", "id": "41", "title": "Similarit\u00e9 : 0.891", "to": "http://cocon.se/actus/black-friday-2018.html", "width": 7.37496678914129 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/near-duplicate/", "id": "42", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/cocon-et-covid19.html", "id": "43", "title": "Similarit\u00e9 : 0.830", "to": "http://cocon.se/actus/", "width": 6.45916745129585 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/", "id": "44", "title": "Similarit\u00e9 : 0.898", "to": "http://cocon.se/a-propos/chercheurs-lab/sophie-la-g-raph/", "width": 7.471454270473247 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/graphe-firebase/", "id": "45", "title": "Similarit\u00e9 : 0.738", "to": "http://cocon.se/cocon-semantique/devs/", "width": 5.076097268375514 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/quelques-news.html", "id": "46", "title": "Similarit\u00e9 : 0.686", "to": "http://cocon.se/a-propos/", "width": 4.296311768991789 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/1s-plus-tard.html", "id": "47", "title": "Similarit\u00e9 : 0.654", "to": "http://cocon.se/historique/point-dimanche.html", "width": 3.8275567344039967 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/", "id": "48", "title": "Similarit\u00e9 : 0.668", "to": "http://cocon.se/safari/annick-marie/", "width": 4.035418189237507 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/export-freemind/", "id": "49", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/cyber-monday.html", "id": "50", "title": "Similarit\u00e9 : 0.619", "to": "http://cocon.se/actus/cache-cocon.html", "width": 3.2974584703995506 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/interlinking.html", "id": "51", "title": "Similarit\u00e9 : 0.752", "to": "http://cocon.se/historique/ajoute-pages.html", "width": 5.290543030290867 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/ecommerce-pb.html", "id": "52", "title": "Similarit\u00e9 : 0.739", "to": "http://cocon.se/cas/os-commerce-jl.html", "width": 5.101258026770489 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/promo-rebelote.html", "id": "53", "title": "Similarit\u00e9 : 0.588", "to": "http://cocon.se/actus/bientot.html", "width": 2.8351075708493676 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/tutos-cocon/crawl-site/", "id": "54", "title": "Similarit\u00e9 : 0.744", "to": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "width": 5.174045748759227 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/mesurer-ttfb/", "id": "55", "title": "Similarit\u00e9 : 0.501", "to": "http://cocon.se/cas/jouer.html", "width": 1.5367860362206014 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/annick-marie/", "id": "56", "title": "Similarit\u00e9 : 0.708", "to": "http://cocon.se/safari/alain-kbiz/", "width": 4.6356881128260525 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/seos-heros-data.html", "id": "57", "title": "Similarit\u00e9 : 0.637", "to": "http://cocon.se/cas/astuce1.html", "width": 3.5640979328714195 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/", "id": "58", "title": "Similarit\u00e9 : 0.653", "to": "http://cocon.se/a-propos/chercheurs-lab/clients/", "width": 3.8029784133599356 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/tutos-cocon/trouver-url/", "id": "59", "title": "Similarit\u00e9 : 0.621", "to": "http://cocon.se/cocon-semantique/images/cocon-et-cluster/", "width": 3.3325251234641198 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/aujourdhui-demain.html", "id": "60", "title": "Similarit\u00e9 : 0.627", "to": "http://cocon.se/concret/", "width": 3.4172059842538998 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/tutos-cocon/", "id": "61", "title": "Similarit\u00e9 : 0.719", "to": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "width": 4.79907026319501 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/inutiles/", "id": "62", "title": "Similarit\u00e9 : 0.778", "to": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "width": 5.675020361336372 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/mot-cle-concurrentiel.html", "id": "63", "title": "Similarit\u00e9 : 0.729", "to": "http://cocon.se/cas/reve-cocon.html", "width": 4.945064027413801 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/", "id": "64", "title": "Similarit\u00e9 : 0.738", "to": "http://cocon.se/cocon-semantique/devs/graphe-firebase/", "width": 5.076097268375514 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/cocon-pousse.html", "id": "65", "title": "Similarit\u00e9 : 0.659", "to": "http://cocon.se/cas/wordpress-avant-apres.html", "width": 3.8925997343131304 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/sego_seo/", "id": "66", "title": "Similarit\u00e9 : 0.824", "to": "http://cocon.se/safari/alain-kbiz/", "width": 6.370892069029529 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/microdatas/", "id": "67", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/bientot.html", "id": "68", "title": "Similarit\u00e9 : 0.649", "to": "http://cocon.se/actus/quelques-news.html", "width": 3.74389001031864 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/jouer.html", "id": "69", "title": "Similarit\u00e9 : 0.664", "to": "http://cocon.se/actus/etudes-vlc.html", "width": 3.975067689945764 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/le-cas-npc.html", "id": "70", "title": "Similarit\u00e9 : 0.735", "to": "http://cocon.se/cas/reve-cocon.html", "width": 5.031240672947045 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/baara-digital/", "id": "71", "title": "Similarit\u00e9 : 0.714", "to": "http://cocon.se/safari/alightweb/", "width": 4.713887239727489 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/cocon-seo/", "id": "72", "title": "Similarit\u00e9 : 0.706", "to": "http://cocon.se/cocon-semantique/", "width": 4.60055278031888 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/art-cocon.html", "id": "73", "title": "Similarit\u00e9 : 0.605", "to": "http://cocon.se/actus/promo1.html", "width": 3.0912818917618674 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/dimension-fractale.html", "id": "74", "title": "Similarit\u00e9 : 0.731", "to": "http://cocon.se/cocon-semantique/images/organique/", "width": 4.9808593961226215 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/vitesse-superieure.html", "id": "75", "title": "Similarit\u00e9 : 0.627", "to": "http://cocon.se/actus/etudes-vlc.html", "width": 3.4151027878167572 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/sophie-la-g-raph/", "id": "76", "title": "Similarit\u00e9 : 0.898", "to": "http://cocon.se/a-propos/chercheurs-lab/", "width": 7.471454270473247 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/metamots/", "id": "77", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/suggestions/near-duplicate/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/faire-un-don/", "id": "78", "title": "Similarit\u00e9 : 0.588", "to": "http://cocon.se/actus/bientot.html", "width": 2.8273869311717146 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/clients/", "id": "79", "title": "Similarit\u00e9 : 0.653", "to": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/", "width": 3.8029784133599356 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/visu-cmap/", "id": "80", "title": "Similarit\u00e9 : 0.650", "to": "http://cocon.se/cocon-semantique/images/cocon-et-cluster/", "width": 3.764343997043216 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/organique/", "id": "81", "title": "Similarit\u00e9 : 0.774", "to": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "width": 5.622186247276097 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/meta-promo-48h.html", "id": "82", "title": "Similarit\u00e9 : 0.678", "to": "http://cocon.se/actus/black-friday-2018.html", "width": 4.180557474157409 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/le-cas-lks.html", "id": "83", "title": "Similarit\u00e9 : 0.747", "to": "http://cocon.se/cas/wordpress-avant-apres.html", "width": 5.217759768006195 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/concret/pre-prod/", "id": "84", "title": "Similarit\u00e9 : 0.800", "to": "http://cocon.se/concret/", "width": 6.01226585856829 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/groups/crawl/", "id": "85", "title": "Similarit\u00e9 : 0.918", "to": "http://cocon.se/suggestions/", "width": 7.767745413333735 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/clients/philippe-donnart/", "id": "86", "title": "Similarit\u00e9 : 0.615", "to": "http://cocon.se/actus/quelques-news.html", "width": 3.2403278774428728 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/", "id": "87", "title": "Similarit\u00e9 : 0.707", "to": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "width": 4.616009222668007 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/analyse-logs-bots.html", "id": "88", "title": "Similarit\u00e9 : 0.702", "to": "http://cocon.se/concret/logs-bea/", "width": 4.538841179346441 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/historique/point-dimanche.html", "id": "89", "title": "Similarit\u00e9 : 0.659", "to": "http://cocon.se/visualisation/", "width": 3.9016698804409087 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/gagner-tableau.html", "id": "90", "title": "Similarit\u00e9 : 0.605", "to": "http://cocon.se/actus/safari-deauville.html", "width": 3.082330373762223 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/autres/", "id": "91", "title": "Similarit\u00e9 : 0.465", "to": "http://cocon.se/actus/cocon-profitez.html", "width": 1.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/black-friday-2018.html", "id": "92", "title": "Similarit\u00e9 : 0.891", "to": "http://cocon.se/actus/promo-black-friday-cocon.html", "width": 7.37496678914129 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/cocon-feu/", "id": "93", "title": "Similarit\u00e9 : 0.713", "to": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "width": 4.700205759596308 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/gests/", "id": "94", "title": "Similarit\u00e9 : 0.692", "to": "http://cocon.se/a-propos/chercheurs-lab/gests/jerome-rossignol/", "width": 4.3900966696602435 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/clients/viaprestige/", "id": "95", "title": "Similarit\u00e9 : 0.619", "to": "http://cocon.se/a-propos/chercheurs-lab/clients/julien-korleon/", "width": 3.2914690878400035 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/", "id": "96", "title": "Similarit\u00e9 : 0.830", "to": "http://cocon.se/actus/cocon-et-covid19.html", "width": 6.45916745129585 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/", "id": "97", "title": "Similarit\u00e9 : 0.816", "to": "http://cocon.se/cas/astuce1.html", "width": 6.241817530093247 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/christian/", "id": "98", "title": "Similarit\u00e9 : 0.613", "to": "http://cocon.se/a-propos/chercheurs-lab/sylvain/", "width": 3.2141155208293264 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/concret/logs-bea/", "id": "99", "title": "Similarit\u00e9 : 0.702", "to": "http://cocon.se/actus/analyse-logs-bots.html", "width": 4.538841179346441 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/crawl-plusieurs-hosts/", "id": "100", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cas/astuce1.html", "id": "101", "title": "Similarit\u00e9 : 0.816", "to": "http://cocon.se/cas/", "width": 6.241817530093247 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/vlc-2015/", "id": "102", "title": "Similarit\u00e9 : 0.699", "to": "http://cocon.se/actus/vlc-2015.html", "width": 4.501095136079508 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/etudes-vlc.html", "id": "103", "title": "Similarit\u00e9 : 0.707", "to": "http://cocon.se/cas/premiere-etude.html", "width": 4.612527977674722 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/concret/", "id": "104", "title": "Similarit\u00e9 : 0.800", "to": "http://cocon.se/concret/pre-prod/", "width": 6.01226585856829 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/optimiser-recherche.html", "id": "105", "title": "Similarit\u00e9 : 0.606", "to": "http://cocon.se/historique/cocon-pousse.html", "width": 3.100496532301358 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/gests/jerome-rossignol/", "id": "106", "title": "Similarit\u00e9 : 0.692", "to": "http://cocon.se/a-propos/chercheurs-lab/gests/", "width": 4.3900966696602435 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/groups/metamots/", "id": "107", "title": "Similarit\u00e9 : 0.658", "to": "http://cocon.se/suggestions/", "width": 3.880656646826558 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/seo-camp-metamots.html", "id": "108", "title": "Similarit\u00e9 : 0.649", "to": "http://cocon.se/actus/cache-cocon.html", "width": 3.7442093251297077 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/", "id": "109", "title": "Similarit\u00e9 : 0.706", "to": "http://cocon.se/cocon-semantique/cocon-seo/", "width": 4.60055278031888 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/devs/optimisation-php/", "id": "110", "title": "Similarit\u00e9 : 0.674", "to": "http://cocon.se/actus/vlc-2015.html", "width": 4.116875575928971 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/mai-fais-te-plait.html", "id": "111", "title": "Similarit\u00e9 : 0.689", "to": "http://cocon.se/actus/promo-black-friday-cocon.html", "width": 4.3522507290222485 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/visualisation/tutos-cocon/inscription-beta/", "id": "112", "title": "Similarit\u00e9 : 0.744", "to": "http://cocon.se/visualisation/tutos-cocon/crawl-site/", "width": 5.174045748759227 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/cocon-profitez.html", "id": "113", "title": "Similarit\u00e9 : 0.599", "to": "http://cocon.se/safari/christelle-milan/", "width": 3.000559023904571 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/suggestions/coeurs-cocon/", "id": "114", "title": "Similarit\u00e9 : 1.000", "to": "http://cocon.se/metamots/", "width": 9.0 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/witamine/", "id": "115", "title": "Similarit\u00e9 : 0.765", "to": "http://cocon.se/safari/sego_seo/", "width": 5.4890560534629325 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/safari/nico-earth/", "id": "116", "title": "Similarit\u00e9 : 0.705", "to": "http://cocon.se/safari/alain-kbiz/", "width": 4.584258805546311 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/canonical-cassees/", "id": "117", "title": "Similarit\u00e9 : 0.842", "to": "http://cocon.se/cocon-semantique/images/url-dupliquees/", "width": 6.633317111159796 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/safari-deauville.html", "id": "118", "title": "Similarit\u00e9 : 0.669", "to": "http://cocon.se/actus/cache-cocon.html", "width": 4.045006552977661 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/a-propos/chercheurs-lab/gests/christian-meline/", "id": "119", "title": "Similarit\u00e9 : 0.528", "to": "http://cocon.se/a-propos/chercheurs-lab/christian/", "width": 1.942970530833 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/cocon-semantique/images/cocon-et-cluster/", "id": "120", "title": "Similarit\u00e9 : 0.731", "to": "http://cocon.se/cocon-semantique/images/canonical-cassees/", "width": 4.974950288236249 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/cache-cocon.html", "id": "121", "title": "Similarit\u00e9 : 0.669", "to": "http://cocon.se/actus/safari-deauville.html", "width": 4.045006552977661 }, { "arrows": "to", "color": "gray", "from": "http://cocon.se/actus/partager-crawls.html", "id": "122", "title": "Similarit\u00e9 : 0.621", "to": "http://cocon.se/actus/bientot.html", "width": 3.3219377860133648 }]);

            nodeColors = {};
            allNodes = nodes.get({ returnType: "Object" });
            for (nodeId in allNodes) {
                nodeColors[nodeId] = allNodes[nodeId].color;
            }
            allEdges = edges.get({ returnType: "Object" });
            // adding nodes and edges to the graph
            data = { nodes: nodes, edges: edges };

            var options = { "physics": { "enabled": true, "stabilization": { "enabled": true, "iterations": 100 }, "barnesHut": { "theta": 0.5, "gravitationalConstant": -8000, "centralGravity": 0.3, "springLength": 95, "springConstant": 0.04, "damping": 0.09, "avoidOverlap": 0.1 }, "minVelocity": 0.75 }, "nodes": { "font": { "size": 12 } }, "edges": { "font": { "size": 10 } }, "interaction": { "hover": true, "tooltipDelay": 200, "dragNodes": true, "dragView": true, "zoomView": true }, "configure": { "enabled": true, "filter": ["nodes", "edges", "physics"], "showButton": true } };






            network = new vis.Network(container, data, options);









            network.on("stabilizationProgress", function (params) {
                document.getElementById('loadingBar').removeAttribute("style");
                var maxWidth = 496;
                var minWidth = 20;
                var widthFactor = params.iterations / params.total;
                var width = Math.max(minWidth, maxWidth * widthFactor);
                document.getElementById('bar').style.width = width + 'px';
                document.getElementById('text').innerHTML = Math.round(widthFactor * 100) + '%';
            });
            network.once("stabilizationIterationsDone", function () {
                document.getElementById('text').innerHTML = '100%';
                document.getElementById('bar').style.width = '496px';
                document.getElementById('loadingBar').style.opacity = 0;
                // really clean the dom element
                setTimeout(function () { document.getElementById('loadingBar').style.display = 'none'; }, 500);
            });


            return network;

        }
        drawGraph();
    </script>
</body>

</html>