import asyncio
import itertools
import json
import os
from typing import Any

# https://github.com/GateNLP/ultimate-sitemap-parser
from usp.tree import sitemap_tree_for_homepage

# https://github.com/UKPLab/sentence-transformers
from sentence_transformers import SentenceTransformer, util

# https://github.com/adbar/trafilatura
import trafilatura

# https://github.com/networkx/networkx
import networkx as nx

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.panel import Panel

# https://github.com/autoscrape-labs/pydoll
from pydoll.browser import Chrome
from pydoll.protocol.network.events import NetworkEvent
from functools import partial

console = Console()


async def scraping(urls) -> dict[Any, Any]:
    async with Chrome() as browser:
        site_responses = {}
        status_codes = {}
        current_url = None

        async def on_response(tab, event):
            """Capture HTTP status codes from network responses"""
            response_url = event["params"]["response"]["url"]
            status = event["params"]["response"]["status"]
            mime_type = event["params"]["response"].get("mimeType", "")

            if (
                mime_type.startswith("text/html")
                and current_url
                and (
                    response_url == current_url
                    or response_url.rstrip("/") == current_url.rstrip("/")
                )
            ):
                status_codes[current_url] = status

        tab = await browser.start(headless=True)

        await tab.enable_network_events()
        await tab.on(NetworkEvent.RESPONSE_RECEIVED, partial(on_response, tab))

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Extraction en cours...", total=len(urls))

            for url in urls:
                progress.update(task, description=f"Traitement: {url[:50]}...")

                console.print(f"🔍 Extraction du contenu de la page: '{url}'...")

                current_url = url

                try:
                    await tab.go_to(url)

                    # Wait a bit for network events to be processed
                    await asyncio.sleep(1)

                    actual_url_result = await tab.execute_script(
                        "return window.location.href"
                    )
                    actual_url = (
                        actual_url_result.get("result", {})
                        .get("result", {})
                        .get("value", url)
                    )

                    title_result = await tab.execute_script("return document.title")
                    title = (
                        title_result.get("result", {})
                        .get("result", {})
                        .get("value", "No Title")
                    )

                    html_result = await tab.execute_script(
                        "return document.documentElement.outerHTML"
                    )
                    html_code = (
                        html_result.get("result", {}).get("result", {}).get("value", "")
                    )

                    links = await tab.find(tag_name="a", find_all=True)
                    link_count = (
                        len(links) if isinstance(links, list) else (1 if links else 0)
                    )

                    status_code = status_codes.get(url, 200)

                    site_responses[url] = {
                        "requested_url": url,
                        "actual_url": actual_url,
                        "title": title,
                        "html_code": html_code,
                        "link_count": link_count,
                        "status_code": status_code,
                    }
                except (RuntimeError, ConnectionError, ValueError) as e:
                    console.print(
                        f"[red]❌ Erreur lors du traitement de {url}: {e}[/red]"
                    )
                    status_code = status_codes.get(url, 500)

                    site_responses[url] = {
                        "requested_url": url,
                        "actual_url": url,
                        "title": "Error",
                        "html_code": "",
                        "link_count": 0,
                        "status_code": status_code,
                        "error": str(e),
                    }

                progress.advance(task)

        return site_responses


async def scrap_html() -> None:
    console.print(
        Panel.fit("🚀 Analyseur de Similarité HTML Français", style="bold magenta")
    )

    console.print("[cyan]📡 Récupération des URLs depuis le sitemap ...[/cyan]")
    site_urls = []

    # site_url ="https://zonetuto.fr/"
    site_url = "http://cocon.se/"
    # site_url = "https://www.patrickcoquart.com/"

    sitemap_tree = sitemap_tree_for_homepage(
        homepage_url=site_url,
        use_known_paths=False,
    )
    if len(list(sitemap_tree.all_pages())) == 0:
        sitemap_tree = sitemap_tree_for_homepage(
            homepage_url=site_url,
            use_known_paths=True,
        )

    for page in sitemap_tree.all_pages():
        site_urls.append(page.url)
    console.print(f"✅ {len(site_urls)} URLs trouvées dans le sitemap")

    site_responses = await scraping(site_urls)

    site_name = (
        site_url.replace("https://", "")
        .replace("http://", "")
        .replace("/", "")
        .replace(".", "_")
    )
    json_filename = f"site_responses_{site_name}.json"
    try:
        with open(json_filename, "w", encoding="utf-8") as json_file:
            json.dump(site_responses, json_file, ensure_ascii=False, indent=2)
        console.print(f"💾 Sauvegarde des données:'{json_filename}'")
    except (IOError, json.JSONDecodeError) as e:
        console.print(
            f"[red]❌ Erreur lors de la sauvegarde: '{json_filename}' Erreur: {e}[/red]"
        )


def load_site_responses(file_path) -> Any | None:
    """
    Charge le fichier JSON contenant les réponses des sites web dans un dictionnaire.

    Args:
        file_path (str): Chemin vers le fichier JSON à charger

    Returns:
        dict: Dictionnaire contenant les données du fichier JSON

    Raises:
        FileNotFoundError: Si le fichier n'existe pas
        json.JSONDecodeError: Si le fichier JSON est mal formaté
    """
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Le fichier {file_path} n'existe pas.")

        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)

        console.print(f"✅ Fichier {file_path} chargé, # Urls a traiter: {len(data)}")

        return data
    except (TypeError, OSError, FileNotFoundError, json.JSONDecodeError) as exc:
        console.print(
            f"[red]❌ Erreur lors du chargement: '{file_path}' Erreur: {exc}[/red]"
        )
        return None


def extract_content(file_path="site_responses_cocon_se.json") -> None:
    """
    Extrait le contenu Texte des réponses des pages HTML des site web à partir du fichier JSON.

    Args:
        file_path (str): Chemin vers le fichier JSON contenant les réponses des sites web.

    Returns:
        list: Liste de tuples contenant l'URL et le contenu HTML
    """
    console.print("🔍 Extraction du contenu texte des pages")

    site_responses = load_site_responses(file_path)
    if not site_responses:
        return

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        transient=True,
        console=console,
    ) as progress:
        task = progress.add_task("Extraction en cours...", total=len(site_responses))

        for url, response in site_responses.items():
            progress.update(task, description=f"Traitement: {url}...")

            trafilatura_data = trafilatura.extract(
                filecontent=response.get("html_code", ""),
                favor_recall=True,
                output_format="json",
                with_metadata=True,
                include_links=False,
                include_tables=True,
                include_formatting=False,
                include_comments=False,
                config=None,
            )

            if trafilatura_data:
                content_metadata = json.loads(trafilatura_data)
                site_responses[url]["raw_text"] = content_metadata.get(
                    "raw_text", ""
                ).replace("\n", "")
                site_responses[url]["word_count"] = len(
                    content_metadata.get("raw_text", "").split()
                )
            else:
                console.print(
                    f"[red]❌ Impossible d'extraire le contenu de:[/red] {url}"
                )
                site_responses[url]["raw_text"] = ""
                site_responses[url]["word_count"] = 0

            progress.advance(task)
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump(site_responses, file, ensure_ascii=False, indent=2)
        console.print(f"💾 Sauvegarde des données:'{file_path}'")
    except (IOError, json.JSONDecodeError) as e:
        console.print(
            f"[red]❌ Erreur lors de la sauvegarde: '{file_path}' Erreur: {e}[/red]"
        )


def generate_similarity_graphe(file_path="site_responses_cocon_se.json") -> None:
    console.print("🔗 Génération du graphe de similarité")

    site_responses = load_site_responses(file_path)
    if not site_responses or len(site_responses) < 2:
        console.print(
            "[red]❌ Erreur: Fichier invalide, au moins 2 URLs valides sont nécessaires[/red]"
        )
        return

    contents = {}
    for url, response in site_responses.items():
        contents[url] = response.get("raw_text")
    url_pairs = list(itertools.combinations(contents.keys(), 2))

    console.print(f"🧮 Calcul de la similarité pour {len(url_pairs)} paires d'URLs")

    all_contents = []
    pair_info = []

    for url1, url2 in url_pairs:
        all_contents.extend([contents[url1], contents[url2]])
        pair_info.append((url1, url2))

    # https://huggingface.co/dangvantuan/sentence-camembert-base
    model_name = "dangvantuan/sentence-camembert-base"
    console.print(f"🤖 Calcul des embeddings avec le modèle: '{model_name}'")
    embd_model = SentenceTransformer(model_name, device="cuda")
    embeddings = embd_model.encode(all_contents, show_progress_bar=True)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("Calcul des similarités...", total=len(pair_info))
        results = {}

        for i, (url1, url2) in enumerate(pair_info):
            embedding1 = embeddings[i * 2]
            embedding2 = embeddings[i * 2 + 1]

            similarity = util.cos_sim(embedding1, embedding2).item()
            results[(url1, url2)] = similarity

            progress.advance(task)

        console.print(f"✅ Analyse terminée ! {len(results)} comparaisons effectuées")

        sim_graph = create_similarity_graph(results)
        nx.write_gexf(sim_graph, "urls_similarity_graph.gexf")
        console.print("✅ Graphe sauvegardé dans 'urls_similarity_graph.gexf'"
        )

def create_similarity_graph(results):
    if not results:
        return nx.DiGraph()

    G = nx.DiGraph()

    all_urls = set()
    for url1, url2 in results.keys():
        all_urls.add(url1)
        all_urls.add(url2)

    G.add_nodes_from(all_urls)

    best_matches = {url: {"partner": None, "score": -1.0} for url in all_urls}

    for (url1, url2), score in results.items():
        if score > best_matches[url1]["score"]:
            best_matches[url1] = {"partner": url2, "score": score}

        if score > best_matches[url2]["score"]:
            best_matches[url2] = {"partner": url1, "score": score}

    for url, data in best_matches.items():
        if data["partner"]:
            G.add_edge(url, data["partner"], weight=data["score"])

    return G


if __name__ == "__main__":
    try:
        # asyncio.run(scrap_html())
        # extract_content()
        generate_similarity_graphe()
    except (KeyboardInterrupt, asyncio.CancelledError):
        pass
